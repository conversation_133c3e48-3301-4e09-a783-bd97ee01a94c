import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTableModule } from '@angular/material/table';
import { MatCardModule } from '@angular/material/card';

interface ReconciliationItem {
  expense_headwise: string;
  opening_stock_store: number;
  opening_stock_kitchen: number;
  opening_stock_bar: number;
  opening_stock_total: number;
  purchase: number;
  transfer_in_out: number;
  closing_stock_store: number;
  closing_stock_kitchen: number;
  closing_stock_bar: number;
  closing_stock_total: number;
  consumption: number;
}

interface ReconciliationCategory {
  category_name: string;
  items: ReconciliationItem[];
  category_total: ReconciliationItem;
}

interface ReconciliationData {
  location: string;
  date_from: string;
  date_to: string;
  categories: ReconciliationCategory[];
  fb_total: ReconciliationItem;
  grand_total: ReconciliationItem;
}

@Component({
  selector: 'app-reconciliation-table',
  standalone: true,
  imports: [CommonModule, MatTableModule, MatCardModule],
  templateUrl: './reconciliation-table.component.html',
  styleUrls: ['./reconciliation-table.component.scss']
})
export class ReconciliationTableComponent implements OnInit {
  @Input() reconciliationData: ReconciliationData | null = null;

  displayedColumns: string[] = [
    'expense_headwise',
    'opening_stock_store',
    'opening_stock_kitchen', 
    'opening_stock_bar',
    'opening_stock_total',
    'purchase',
    'transfer_in_out',
    'closing_stock_store',
    'closing_stock_kitchen',
    'closing_stock_bar', 
    'closing_stock_total',
    'consumption'
  ];

  columnHeaders: { [key: string]: string } = {
    'expense_headwise': 'Expense Headwise',
    'opening_stock_store': 'Store',
    'opening_stock_kitchen': 'Kitchen',
    'opening_stock_bar': 'Bar',
    'opening_stock_total': 'Total',
    'purchase': 'Purchase',
    'transfer_in_out': 'Transfer In/Out',
    'closing_stock_store': 'Store',
    'closing_stock_kitchen': 'Kitchen',
    'closing_stock_bar': 'Bar',
    'closing_stock_total': 'Total',
    'consumption': 'Consumption'
  };

  ngOnInit(): void {
    // Component initialization
  }

  formatNumber(value: number): string {
    if (value === 0) return '-';
    return new Intl.NumberFormat('en-IN').format(Math.round(value));
  }

  getCategoryColor(categoryName: string): string {
    const colors: { [key: string]: string } = {
      'Food': '#dc3545',
      'Liquor': '#dc3545', 
      'Beverage': '#dc3545',
      'Tobacco': '#dc3545',
      'Others': '#dc3545'
    };
    return colors[categoryName] || '#dc3545';
  }

  isNumericColumn(column: string): boolean {
    return column !== 'expense_headwise';
  }

  getColumnValue(item: ReconciliationItem, column: string): any {
    return (item as any)[column];
  }
}
