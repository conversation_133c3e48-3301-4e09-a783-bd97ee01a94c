@use '../../../styles/variables' as v;

.dashboard-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.content {
  height: calc(100vh - 40px); // Updated to match actual toolbar height
  border-radius: v.$global-border-radius;
  margin: 0; // Remove margin to allow full height usage
  padding: 0; // Remove padding to allow child components to manage their own spacing
  overflow-y: auto; // Allow scrolling for pages that need it
  background-color: white;

  // Default gray scrollbar styling for consistency
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;

    &:hover {
      background: rgba(0, 0, 0, 0.3);
    }
  }

  // Firefox scrollbar styling
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(100vh - 40px); // Updated to match actual toolbar height
}

.loading-spinner {
  text-align: center;

  .spin {
    font-size: 48px;
    height: 48px;
    width: 48px;
    color: #ff9100;
    animation: spin 1.5s linear infinite;
  }

  p {
    margin-top: 16px;
    font-size: 16px;
    color: #666;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.closingContainer{
  display: flex;
  margin-top: 10vh;
  justify-content: center;
  min-height: 100vh;
}

.closingContainerDatas{
  max-width: 85vw;
  pointer-events: auto;
  width: 550px;
  position: static;
}

.closeMsg{
  text-align: center;
  font-size: large;
  font-weight: bold;
  padding-top: 2rem;
  padding-bottom: 1rem;
}

.closeMsgBtn{
  text-align: center;
}