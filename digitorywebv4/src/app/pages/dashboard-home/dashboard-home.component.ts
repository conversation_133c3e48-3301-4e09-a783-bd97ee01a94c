import { ChangeDetectionStrategy, Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DashboardCardComponent } from 'src/app/components/dashboard-card/dashboard-card.component';
import { BackgroundImageCardComponent } from 'src/app/components/background-image-card/background-image-card.component';
import { MatIconModule } from '@angular/material/icon';

import { Router } from '@angular/router';
import { MasterDataService } from 'src/app/services/master-data.service';
import { first } from 'rxjs';

@Component({
  selector: 'app-dashboard-home',
  standalone: true,
  imports: [CommonModule, DashboardCardComponent, BackgroundImageCardComponent, MatIconModule],
  templateUrl: './dashboard-home.component.html',
  styleUrls: ['./dashboard-home.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DashboardHomeComponent {
  constructor(
    private masterDataService: MasterDataService,
    private router:Router,
    ) {
      this.masterDataService.route$.pipe(first()).subscribe(tab => {
        if ((tab && tab === 'user') || (tab && tab === 'Roles') || (tab && tab === 'branches')){
          this.router.navigate(['/dashboard/user']) ;
        }else if ((tab && tab === 'menu master') || (tab && tab === 'Subrecipe Master') || (tab && tab === 'servingsize conversion')){
            this.router.navigate(['/dashboard/recipe']) ;
        }else if (tab && tab === 'party'){
          this.router.navigate(['/dashboard/party']) ;
        }else{
          this.router.navigate(['/dashboard/inventory']) ;
        }
      })
  }
}
